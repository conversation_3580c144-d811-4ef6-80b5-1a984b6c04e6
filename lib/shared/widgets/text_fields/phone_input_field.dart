import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/core/services/country_service.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/dropdowns/country_dropdown.dart';

class PhoneInputField extends StatefulWidget {
  final TextEditingController controller;
  final String? hintText;
  final String? errorText;
  final ValueChanged<String>? onChanged;
  final ValueChanged<Country>? onCountryChanged;
  final Country? selectedCountry;
  final bool enabled;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final VoidCallback? onTap;

  const PhoneInputField({
    super.key,
    required this.controller,
    this.hintText,
    this.errorText,
    this.onChanged,
    this.onCountryChanged,
    this.selectedCountry,
    this.enabled = true,
    this.prefixIcon,
    this.suffixIcon,
    this.onTap,
  });

  @override
  State<PhoneInputField> createState() => _PhoneInputFieldState();
}

class _PhoneInputFieldState extends State<PhoneInputField> {
  Country? _selectedCountry;

  @override
  void initState() {
    super.initState();
    _initializeCountry();
  }

  Future<void> _initializeCountry() async {
    if (widget.selectedCountry != null) {
      _selectedCountry = widget.selectedCountry;
    } else {
      _selectedCountry = await CountryService.instance.getDefaultCountry();
    }
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 48.gw,
          decoration: BoxDecoration(
            border: Border.all(
              color: widget.errorText != null ? Colors.red : context.colorTheme.textSecondary.withOpacity(0.3),
            ),
            borderRadius: BorderRadius.circular(8.gw),
          ),
          child: Row(
            children: [
              // Country dropdown
              if (_selectedCountry != null)
                CountryDropdown(
                  selectedCountry: _selectedCountry,
                  onCountryChanged: (country) {
                    setState(() {
                      _selectedCountry = country;
                    });
                    widget.onCountryChanged?.call(country);
                  },
                  showFullName: true, // Show full name in dropdown menu
                  showBorder: false, 
                  width: 50.gw, 
                  height: 60.gh,
                ),

              // Divider
              Container(
                width: 1.gw,
                height: 24.gw,
                color: context.colorTheme.textSecondary.withOpacity(0.3),
                margin: EdgeInsets.symmetric(horizontal: 4.gw), 
              ),

              // Phone number input
              Expanded(
                child: TextFormField(
                  controller: widget.controller,
                  enabled: widget.enabled,
                  onChanged: widget.onChanged,
                  onTap: widget.onTap,
                  keyboardType: TextInputType.phone,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(15),
                  ],
                  style: TextStyle(
                    fontSize: 16.gw,
                    color: context.colorTheme.textPrimary,
                  ),
                  decoration: InputDecoration(
                    hintText: widget.hintText ?? 'phone_number'.tr(),
                    hintStyle: context.textTheme.highlight,
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12.gw,
                      vertical: 12.gw,
                    ),
                    prefixIcon: widget.prefixIcon,
                    suffixIcon: widget.suffixIcon,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Error text
        if (widget.errorText != null) ...[
          SizedBox(height: 4.gw),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.gw),
            child: Text(
              widget.errorText!,
              style: TextStyle(
                fontSize: 12.gw,
                color: Colors.red,
              ),
            ),
          ),
        ],
      ],
    );
  }
}
