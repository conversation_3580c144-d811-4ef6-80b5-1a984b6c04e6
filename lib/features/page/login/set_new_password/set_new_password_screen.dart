import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/shared/widgets/animation/scale_animation.dart';
import 'package:wd/shared/widgets/auth/auth_tab_bar.dart';
import 'package:wd/shared/widgets/text_fields/phone_input_field.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/icon_textfield.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/core/services/country_service.dart';

import '../../../../core/utils/system_util.dart';
import '../forgot/forgot_cubit.dart';

class SetNewPasswordPage extends StatefulWidget {
  final LoginType resetType;
  final String contactInfo; // phone or email

  const SetNewPasswordPage({
    super.key,
    required this.resetType,
    required this.contactInfo,
  });

  @override
  State<SetNewPasswordPage> createState() => _SetNewPasswordPageState();
}

class _SetNewPasswordPageState extends State<SetNewPasswordPage> {
  Country? _selectedCountry;

  static const double _verticalSpacing = 15.0;
  static const double _sectionSpacing = 30.0;

  @override
  void initState() {
    super.initState();
    _initializeCountry();
  }

  Future<void> _initializeCountry() async {
    final defaultCountry = await CountryService.instance.getDefaultCountry();
    if (mounted) {
      setState(() {
        _selectedCountry = defaultCountry;
      });
    }
  }

  String emojiFlag(String countryCode) {
    return countryCode.toUpperCase().runes.map((codeUnit) => String.fromCharCode(0x1F1E6 + (codeUnit - 0x41))).join();
  }

  @override
  Widget build(BuildContext context) {
    return _buildSliverLayout();
  }

  /// Builds the sliver layout with persistent header matching login design
  Widget _buildSliverLayout() {
    final screenHeight = MediaQuery.of(context).size.height;
    final headerHeight = screenHeight * 0.36; // ~400px on 1252px screen - same as login

    return CustomScrollView(
      slivers: [
        // Header section with title
        SliverPersistentHeader(
          pinned: false,
          floating: false,
          delegate: _SetPasswordHeaderDelegate(
            minHeight: headerHeight * 0.6, // Minimum height when collapsed
            maxHeight: headerHeight, // Full height when expanded
            title: _buildTitle(),
            subtitle: _buildSubtitle(),
          ),
        ),
        // Content section
        SliverToBoxAdapter(
          child: Container(
            decoration: BoxDecoration(
              color: context.theme.scaffoldBackgroundColor,
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.gw),
              child: Column(
                children: [
                  SizedBox(height: 20.gw),
                  // Tab bar for phone/email selection
                  PhoneEmailTabBar(
                    selectedIndex: widget.resetType == LoginType.phone ? 0 : 1,
                    onTabChanged: (index) {
                      // Tab switching is handled by parent widget
                    },
                  ),
                  SizedBox(height: _sectionSpacing.gw),
                  // Form content
                  BlocBuilder<ForgotCubit, ForgotState>(
                    builder: (context, state) {
                      return CommonScaleAnimationWidget(
                        children: [
                          if (widget.resetType == LoginType.phone) ...[
                            _buildPhoneInputField(),
                          ] else ...[
                            _buildEmailInputField(),
                          ],
                          SizedBox(height: _verticalSpacing.gw),
                          _buildVerificationCodeInputField(),
                          SizedBox(height: _verticalSpacing.gw),
                          _buildPasswordInput(),
                          SizedBox(height: _verticalSpacing.gw),
                          _buildConfirmPasswordInput(),
                          SizedBox(height: _sectionSpacing.gw),
                          _buildSetPasswordButton(),
                          SizedBox(height: 20.gw),
                          _buildBottomText(),
                          SizedBox(height: 40.gw), // Extra bottom padding
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the title for the header
  Widget _buildTitle() {
    return Text(
      'Set New',
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 28.gw,
        fontWeight: FontWeight.w400,
        color: Colors.white,
        height: 1.2,
      ),
    );
  }

  /// Builds the subtitle for the header
  Widget _buildSubtitle() {
    String subtitle;

    switch (widget.resetType) {
      case LoginType.phone:
        subtitle = 'Password via Phone';
        break;
      case LoginType.email:
        subtitle = 'Password via Email';
        break;
      default:
        subtitle = 'Password';
    }

    return Text(
      subtitle,
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 28.gw,
        fontWeight: FontWeight.bold,
        color: Colors.white,
        height: 1.2,
      ),
    );
  }

  /// Builds the phone input field with country code dropdown
  Widget _buildPhoneInputField() {
    return PhoneInputField(
      controller: TextEditingController()..text = widget.contactInfo,
      hintText: "hint_enter_phone".tr(),
      selectedCountry: _selectedCountry,
      enabled: false, // Read-only since it's already verified
      onCountryChanged: (country) {
        setState(() {
          _selectedCountry = country;
        });
      },
    );
  }

  /// Builds the email input field
  Widget _buildEmailInputField() {
    return IconTextfield(
      textController: TextEditingController()..text = widget.contactInfo,
      hintText: "Enter email",
      icon: IconButton(
        icon: Image.asset(Assets.iconEmail, width: 20.gw, height: 20.gw),
        onPressed: () {},
      ),
    );
  }

  /// Builds the verification code input field
  Widget _buildVerificationCodeInputField() {
    return Container(
      padding: EdgeInsets.fromLTRB(4.gw, 0, 4.gw, 0),
      decoration: BoxDecoration(
        color: context.colorTheme.foregroundColor,
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Row(
        children: [
          IconButton(
            icon: Image.asset(Assets.iconLoginShield, width: 20.gw, height: 20.gw),
            onPressed: () {},
          ),
          Expanded(
            child: TextField(
              controller: TextEditingController(),
              style: context.textTheme.primary.copyWith(
                fontSize: 16.gw,
                color: context.colorTheme.textPrimary,
              ),
              decoration: InputDecoration(
                hintText: "Please enter the code",
                hintStyle: context.textTheme.primary.copyWith(
                  fontSize: 16.gw,
                  color: context.colorTheme.textSecondary,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(vertical: 15.gw),
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.all(4.gw),
            child: _buildGetCodeButton(),
          ),
        ],
      ),
    );
  }

  /// Builds the password input field
  Widget _buildPasswordInput() {
    return IconTextfield(
      textController: TextEditingController(),
      hintText: "Password must be 6—22 letters or digits",
      icon: IconButton(
        icon: Image.asset(Assets.iconLoginPassword, width: 20.gw, height: 20.gw),
        onPressed: () {},
      ),
    );
  }

  /// Builds the confirm password input field
  Widget _buildConfirmPasswordInput() {
    return IconTextfield(
      textController: TextEditingController(),
      hintText: "Confirm Password",
      icon: IconButton(
        icon: Image.asset(Assets.iconLoginPassword, width: 20.gw, height: 20.gw),
        onPressed: () {},
      ),
    );
  }

  /// Builds the get code button
  Widget _buildGetCodeButton() {
    return Container(
      margin: EdgeInsets.all(8.gw),
      child: CommonButton(
        title: "Get code",
        width: 80.gw,
        height: 32.gw,
        textColor: Colors.black,
        backgroundColor: context.theme.primaryColor,
        onPressed: () {
          // TODO: Implement get code functionality for set new password
        },
      ),
    );
  }

  /// Builds the set password button
  Widget _buildSetPasswordButton() {
    return CommonButton(
      title: "Log In",
      textColor: context.colorTheme.btnTitlePrimary,
      onPressed: () {
        // TODO: Implement set new password functionality
      },
    );
  }

  /// Builds the bottom text
  Widget _buildBottomText() {
    return GestureDetector(
      onTap: () => SystemUtil.contactService(),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.gw),
        child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: TextStyle(
              fontSize: 12.fs,
              color: Colors.grey,
            ),
            children: [
              const TextSpan(
                  text:
                      '*Only users with a bound mobile number or email can retrieve their password via self-service. Users without a bound number should contact '),
              TextSpan(
                text: 'Online Support',
                style: TextStyle(
                  color: context.theme.primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
              const TextSpan(text: ' for assistance'),
            ],
          ),
        ),
      ),
    );
  }
}

/// Custom SliverPersistentHeaderDelegate for the set new password header
class _SetPasswordHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget? title;
  final Widget? subtitle;

  _SetPasswordHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    this.title,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    final progress = (shrinkOffset / (maxHeight - minHeight)).clamp(0.0, 1.0);
    final opacity = 1.0 - progress;

    return Container(
      width: double.infinity,
      height: maxHeight,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/login/bg_login_logo.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: Stack(
        children: [
          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 10.gw,
            left: 15.gw,
            child: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                alignment: Alignment.center,
                child: Image.asset(
                  Assets.iconBack,
                  height: 32.gh,
                  width: 32.gw,
                ),
              ),
            ),
          ),
          // Logo and title section
          Positioned.fill(
            child: Opacity(
              opacity: opacity,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(height: 90.gh),
                  // WD Logo
                  SvgPicture.asset(
                    Assets.tabLogo,
                    width: 131.gw,
                    height: 60.gh,
                  ),
                  SizedBox(height: 20.gh),
                  // Title
                  if (title != null) title!,
                  SizedBox(height: 8.gh),
                  // Subtitle
                  if (subtitle != null) subtitle!,
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return oldDelegate is! _SetPasswordHeaderDelegate ||
        oldDelegate.minHeight != minHeight ||
        oldDelegate.maxHeight != maxHeight ||
        oldDelegate.title != title ||
        oldDelegate.subtitle != subtitle;
  }
}
